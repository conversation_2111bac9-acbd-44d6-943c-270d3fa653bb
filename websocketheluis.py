import websocket
import time
import json
import random


link = "wss://io.dexscreener.com/dex/screener/v5/pair/solana/6kvyjadgrnxbzsfsehcbxwin4hutchna2rrguatsqini"

def on_message(ws, message):
    try:
        # Try to parse as <PERSON><PERSON><PERSON> for better formatting
        data = json.loads(message)
        print(f"Received: {json.dumps(data, indent=2)}")
    except json.JSONDecodeError:
        print(f"Received: {message}")

def on_error(ws, error):
    print(f"WebSocket error: {error}")

def on_close(ws, close_status_code, close_msg):
    print(f"### Connection closed ### Status: {close_status_code}, Message: {close_msg}")

def on_open(ws):
    print("✅ WebSocket connection opened successfully!")

def create_headers():
    """Create browser-like headers to avoid detection"""
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]

    return {
        "User-Agent": random.choice(user_agents),
        "Origin": "https://dexscreener.com",
        "Referer": "https://dexscreener.com/",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache"
    }

if __name__ == "__main__":
    print("🚀 Starting DexScreener WebSocket connection...")
    print(f"📡 Connecting to: {link}")

    # Enable trace for debugging
    websocket.enableTrace(True)

    # Create WebSocket with custom headers
    headers = create_headers()
    print(f"🔧 Using headers: {headers}")

    try:
        ws = websocket.WebSocketApp(
            link,
            header=headers,
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )

        # Add some delay and retry logic
        print("⏳ Attempting connection...")
        ws.run_forever(
            ping_interval=30,  # Send ping every 30 seconds
            ping_timeout=10    # Wait 10 seconds for pong
        )

    except KeyboardInterrupt:
        print("\n🛑 Connection interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("💡 Tip: DexScreener might be blocking WebSocket connections. Consider using their REST API instead.")