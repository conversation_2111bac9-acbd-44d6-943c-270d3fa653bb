import websocket
import time
import json
import random
import ssl
import threading
from urllib.parse import urlparse


class DexScreenerWebSocket:
    """
    Advanced WebSocket client for DexScreener with anti-detection measures
    """
    
    def __init__(self, pair_address):
        self.pair_address = pair_address
        self.url = f"wss://io.dexscreener.com/dex/screener/v5/pair/solana/{pair_address}"
        self.ws = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5
        
    def create_headers(self):
        """Create browser-like headers with rotation"""
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0"
        ]
        
        return {
            "User-Agent": random.choice(user_agents),
            "Origin": "https://dexscreener.com",
            "Referer": "https://dexscreener.com/solana/" + self.pair_address,
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-WebSocket-Extensions": "permessage-deflate; client_max_window_bits",
            "Sec-WebSocket-Version": "13"
        }
    
    def on_message(self, ws, message):
        """Handle incoming messages"""
        try:
            data = json.loads(message)
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n📊 [{timestamp}] Received update:")
            print(json.dumps(data, indent=2))
        except json.JSONDecodeError:
            print(f"📨 Raw message: {message}")
    
    def on_error(self, ws, error):
        """Handle WebSocket errors"""
        print(f"❌ WebSocket error: {error}")
        self.is_connected = False
    
    def on_close(self, ws, close_status_code, close_msg):
        """Handle connection close"""
        print(f"🔌 Connection closed - Status: {close_status_code}, Message: {close_msg}")
        self.is_connected = False
        
        # Attempt reconnection if not manually closed
        if close_status_code != 1000 and self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            print(f"🔄 Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts} in {self.reconnect_delay} seconds...")
            time.sleep(self.reconnect_delay)
            self.connect()
    
    def on_open(self, ws):
        """Handle successful connection"""
        print("✅ WebSocket connection established!")
        self.is_connected = True
        self.reconnect_attempts = 0
        
        # Send any initial subscription messages if needed
        # DexScreener might require specific subscription format
        try:
            subscribe_msg = {
                "type": "subscribe",
                "pair": self.pair_address
            }
            ws.send(json.dumps(subscribe_msg))
            print(f"📡 Sent subscription for pair: {self.pair_address}")
        except Exception as e:
            print(f"⚠️  Could not send subscription: {e}")
    
    def connect(self):
        """Establish WebSocket connection with enhanced settings"""
        try:
            print(f"🚀 Connecting to DexScreener WebSocket...")
            print(f"📡 URL: {self.url}")
            
            # Add random delay to avoid rate limiting
            delay = random.uniform(1, 3)
            print(f"⏳ Random delay: {delay:.2f} seconds")
            time.sleep(delay)
            
            # Create SSL context that's more permissive
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # Enable trace for debugging
            websocket.enableTrace(True)
            
            # Create WebSocket with enhanced configuration
            self.ws = websocket.WebSocketApp(
                self.url,
                header=self.create_headers(),
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            
            # Run with additional options
            self.ws.run_forever(
                sslopt={"cert_reqs": ssl.CERT_NONE},
                ping_interval=30,
                ping_timeout=10,
                origin="https://dexscreener.com"
            )
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            self.is_connected = False
    
    def start_monitoring(self):
        """Start monitoring in a separate thread"""
        def monitor():
            while self.reconnect_attempts < self.max_reconnect_attempts:
                try:
                    self.connect()
                except KeyboardInterrupt:
                    print("\n🛑 Monitoring stopped by user")
                    break
                except Exception as e:
                    print(f"❌ Monitoring error: {e}")
                    time.sleep(self.reconnect_delay)
        
        monitor_thread = threading.Thread(target=monitor)
        monitor_thread.daemon = True
        monitor_thread.start()
        return monitor_thread


def main():
    """Main function"""
    pair_address = "6kvyjadgrnxbzsfsehcbxwin4hutchna2rrguatsqini"
    
    print("🎯 Advanced DexScreener WebSocket Monitor")
    print("=" * 60)
    print(f"📊 Monitoring pair: {pair_address}")
    print("💡 This version includes anti-detection measures")
    print("⚠️  Note: WebSocket connections may still be blocked by Cloudflare")
    print("=" * 60)
    
    # Create and start WebSocket client
    ws_client = DexScreenerWebSocket(pair_address)
    
    try:
        # Start monitoring
        monitor_thread = ws_client.start_monitoring()
        
        # Keep main thread alive
        while True:
            time.sleep(1)
            if not monitor_thread.is_alive():
                break
                
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")
        if ws_client.ws:
            ws_client.ws.close()


if __name__ == "__main__":
    main()
