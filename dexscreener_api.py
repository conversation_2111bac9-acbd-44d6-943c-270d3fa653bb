import requests
import time
import json
from datetime import datetime
import random


class DexScreenerAPI:
    """
    DexScreener REST API client for monitoring Solana token pairs
    More reliable than WebSocket connections which are often blocked
    """
    
    def __init__(self):
        self.base_url = "https://api.dexscreener.com/latest/dex"
        self.session = requests.Session()
        self.setup_session()
    
    def setup_session(self):
        """Setup session with browser-like headers"""
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        self.session.headers.update({
            "User-Agent": random.choice(user_agents),
            "Accept": "application/json",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://dexscreener.com/",
            "Origin": "https://dexscreener.com",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
        })
    
    def get_pair_data(self, pair_address):
        """Get current data for a specific pair"""
        try:
            url = f"{self.base_url}/pairs/solana/{pair_address}"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"❌ Error fetching pair data: {e}")
            return None
    
    def get_token_data(self, token_address):
        """Get data for a specific token across all pairs"""
        try:
            url = f"{self.base_url}/tokens/{token_address}"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"❌ Error fetching token data: {e}")
            return None
    
    def monitor_pair(self, pair_address, interval=10, duration=None):
        """
        Monitor a pair continuously
        
        Args:
            pair_address: The pair address to monitor
            interval: Seconds between requests (default: 10)
            duration: Total monitoring duration in seconds (None for infinite)
        """
        print(f"🚀 Starting monitoring for pair: {pair_address}")
        print(f"⏱️  Update interval: {interval} seconds")
        print(f"⏳ Duration: {'Infinite' if duration is None else f'{duration} seconds'}")
        print("-" * 60)
        
        start_time = time.time()
        
        try:
            while True:
                current_time = time.time()
                
                # Check if duration limit reached
                if duration and (current_time - start_time) >= duration:
                    print(f"⏰ Monitoring duration of {duration} seconds reached")
                    break
                
                # Fetch current data
                data = self.get_pair_data(pair_address)
                
                if data and 'pairs' in data and len(data['pairs']) > 0:
                    pair = data['pairs'][0]
                    self.display_pair_info(pair)
                else:
                    print(f"⚠️  No data received for pair {pair_address}")
                
                print("-" * 60)
                
                # Wait for next update
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    def display_pair_info(self, pair):
        """Display formatted pair information"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"📊 Update at {timestamp}")
        print(f"💰 Price USD: ${float(pair.get('priceUsd', 0)):.8f}")
        print(f"📈 Price Change 5m: {pair.get('priceChange', {}).get('m5', 'N/A')}%")
        print(f"📈 Price Change 1h: {pair.get('priceChange', {}).get('h1', 'N/A')}%")
        print(f"📈 Price Change 6h: {pair.get('priceChange', {}).get('h6', 'N/A')}%")
        print(f"📈 Price Change 24h: {pair.get('priceChange', {}).get('h24', 'N/A')}%")
        print(f"💧 Liquidity USD: ${float(pair.get('liquidity', {}).get('usd', 0)):,.2f}")
        print(f"📊 Volume 5m: ${float(pair.get('volume', {}).get('m5', 0)):,.2f}")
        print(f"📊 Volume 1h: ${float(pair.get('volume', {}).get('h1', 0)):,.2f}")
        print(f"📊 Volume 6h: ${float(pair.get('volume', {}).get('h6', 0)):,.2f}")
        print(f"📊 Volume 24h: ${float(pair.get('volume', {}).get('h24', 0)):,.2f}")
        print(f"🏪 DEX: {pair.get('dexId', 'Unknown')}")
        print(f"🔗 Pair Address: {pair.get('pairAddress', 'N/A')}")


def main():
    """Main function to demonstrate usage"""
    # The pair address from your original WebSocket URL
    pair_address = "6kvyjadgrnxbzsfsehcbxwin4hutchna2rrguatsqini"
    
    # Create API client
    api = DexScreenerAPI()
    
    print("🎯 DexScreener API Monitor")
    print("=" * 60)
    
    # Get initial data
    print("📡 Fetching initial pair data...")
    initial_data = api.get_pair_data(pair_address)
    
    if initial_data:
        print("✅ Successfully connected to DexScreener API!")
        print("📋 Initial pair information:")
        if 'pairs' in initial_data and len(initial_data['pairs']) > 0:
            api.display_pair_info(initial_data['pairs'][0])
        print("=" * 60)
        
        # Start monitoring
        try:
            api.monitor_pair(pair_address, interval=10)  # Update every 10 seconds
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
    else:
        print("❌ Failed to connect to DexScreener API")
        print("💡 Please check your internet connection and try again")


if __name__ == "__main__":
    main()
